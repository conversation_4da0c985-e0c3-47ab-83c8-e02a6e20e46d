#!/usr/bin/env python3
"""
Multi-port proxy server to catch requests from frontend
This will help us identify which port the frontend is trying to use
"""

import http.server
import socketserver
import json
import urllib.parse
import threading
import requests

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, port, *args, **kwargs):
        self.proxy_port = port
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_GET(self):
        self.proxy_request('GET')
    
    def do_POST(self):
        self.proxy_request('POST')
    
    def proxy_request(self, method):
        """Proxy request to backend on port 8000"""
        try:
            print(f"🔄 Port {self.proxy_port}: {method} {self.path}")
            
            # Get request data for POST
            data = None
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    data = self.rfile.read(content_length)
            
            # Forward to backend
            backend_url = f"http://localhost:8000{self.path}"
            
            if method == 'GET':
                response = requests.get(backend_url, timeout=5)
            elif method == 'POST':
                headers = {'Content-Type': 'application/json'} if data else {}
                response = requests.post(backend_url, data=data, headers=headers, timeout=5)
            
            # Send response back
            self.send_response(response.status_code)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.end_headers()
            
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"❌ Port {self.proxy_port}: Error proxying {method} {self.path}: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            error_response = json.dumps({
                "error": "Proxy error",
                "message": str(e),
                "proxy_port": self.proxy_port,
                "backend": "http://localhost:8000"
            })
            self.wfile.write(error_response.encode('utf-8'))

def create_handler(port):
    """Create a handler class with the port number"""
    class Handler(ProxyHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(port, *args, **kwargs)
    return Handler

def start_proxy_server(port):
    """Start a proxy server on the given port"""
    try:
        handler = create_handler(port)
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"✅ Proxy server started on port {port}")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Failed to start proxy on port {port}: {e}")

def main():
    """Start proxy servers on multiple ports"""
    ports = [5000, 5001, 5002, 5003, 3001]  # Common ports frontend might use
    
    print("🔄 Starting multi-port proxy servers")
    print("📡 All requests will be forwarded to http://localhost:8000")
    print("=" * 50)
    
    threads = []
    
    for port in ports:
        try:
            thread = threading.Thread(target=start_proxy_server, args=(port,), daemon=True)
            thread.start()
            threads.append(thread)
        except Exception as e:
            print(f"❌ Failed to start thread for port {port}: {e}")
    
    print(f"🚀 Started proxy servers on ports: {ports}")
    print("🔍 Waiting for frontend requests...")
    print("Press Ctrl+C to stop")
    
    try:
        # Keep main thread alive
        for thread in threads:
            thread.join()
    except KeyboardInterrupt:
        print("\n🛑 Stopping all proxy servers")

if __name__ == "__main__":
    main()
