'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const pathname = usePathname();

  const menuItems = [
    { href: '/dashboard', label: 'Overview', icon: '📊' },
    { href: '/dashboard/train', label: 'Training', icon: '🧠' },
    { href: '/dashboard/playground', label: 'Playground', icon: '🎮' },
    { href: '/dashboard/applications', label: 'Applications', icon: '🚀' },
  ];

  return (
    <div className="min-h-screen bg-secondme-warm-bg">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-secondme-gray-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-secondme-navy">
                Second Me
              </Link>
              <span className="ml-4 text-sm text-secondme-navy/60">Dashboard</span>
            </div>
            <nav className="hidden md:flex space-x-4">
              <Link
                href="/"
                className="px-3 py-2 rounded-md text-sm font-medium text-secondme-navy hover:text-secondme-blue transition-colors"
              >
                ← Back to Home
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-secondme-gray-300 min-h-screen">
          <nav className="p-4">
            <ul className="space-y-2">
              {menuItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      pathname === item.href
                        ? 'bg-secondme-blue text-white'
                        : 'text-secondme-navy hover:bg-secondme-blue/10 hover:text-secondme-blue'
                    }`}
                  >
                    <span className="mr-3">{item.icon}</span>
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
