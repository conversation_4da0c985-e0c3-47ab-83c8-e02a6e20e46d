'use client';

import React from 'react';
import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-secondme-warm-bg">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-secondme-navy mb-6">
            Build Your{' '}
            <span className="text-secondme-blue">AI Self</span>
          </h1>
          <p className="text-xl text-secondme-navy/80 mb-8 max-w-2xl mx-auto">
            Train a personalized AI model with your memories, knowledge, and personality. 
            Create your digital twin that thinks and responds like you.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/dashboard"
              className="btn-primary inline-flex items-center justify-center"
            >
              Get Started
              <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
            <Link
              href="/dashboard/playground"
              className="px-6 py-3 text-base font-medium text-secondme-blue bg-white border-2 border-secondme-blue rounded-xl hover:bg-secondme-blue hover:text-white transition-all duration-200 ease-out hover:-translate-y-0.5 hover:shadow-secondme-blue"
              style={{ width: 'var(--btn-width)' }}
            >
              Try Demo
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-secondme-navy mb-12">
            How Second Me Works
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="card p-6 text-center">
              <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-secondme-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondme-navy mb-3">
                Upload Your Memories
              </h3>
              <p className="text-secondme-navy/70">
                Share your personal experiences, knowledge, and thoughts to create a comprehensive dataset.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="card p-6 text-center">
              <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-secondme-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondme-navy mb-3">
                AI Training
              </h3>
              <p className="text-secondme-navy/70">
                Our advanced AI system learns from your data to understand your personality and knowledge.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="card p-6 text-center">
              <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-secondme-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondme-navy mb-3">
                Chat with Your AI
              </h3>
              <p className="text-secondme-navy/70">
                Interact with your personalized AI that responds with your unique voice and perspective.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Status Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-secondme-navy mb-8">
            System Status
          </h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-secondme-navy mb-3">
                Frontend Service
              </h3>
              <div className="flex items-center justify-center">
                <div className="w-3 h-3 bg-secondme-accent-green rounded-full mr-2"></div>
                <span className="text-secondme-navy">Running on port 3001</span>
              </div>
            </div>
            
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-secondme-navy mb-3">
                Training System
              </h3>
              <div className="flex items-center justify-center">
                <div className="w-3 h-3 bg-secondme-accent-green rounded-full mr-2"></div>
                <span className="text-secondme-navy">Model: test_production</span>
              </div>
            </div>
          </div>
          
          <div className="mt-8">
            <Link
              href="/dashboard/train"
              className="inline-flex items-center text-secondme-blue hover:text-secondme-blue-hover transition-colors"
            >
              View Training Status
              <svg className="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
