'use client';

import React from 'react';

interface ApplicationCard {
  title: string;
  subtitle?: string;
  description: string;
  icon: string;
  status: 'available' | 'coming-soon' | 'demo';
}

const applications: ApplicationCard[] = [
  {
    title: 'API & MCP',
    description:
      'APIs and MCPs allow you to build custom Second Me apps and extend its functionality.',
    icon: '🔌',
    status: 'demo'
  },
  {
    title: 'Roleplay Apps',
    description:
      'Give your Second Me different personas to express themselves naturally in various scenarios.',
    icon: '🎭',
    status: 'demo'
  },
  {
    title: 'Network Apps',
    description:
      'Create spaces where multiple Second Mes work together to complete shared missions.',
    icon: '🌐',
    status: 'coming-soon'
  },
  {
    title: 'Second X Apps',
    description:
      'Future services natively-built for Second Me to use: Second Tinder, Second Linkedin, etc.',
    icon: '🚀',
    status: 'coming-soon'
  },
  {
    title: 'Integrations',
    description: 'Integrate Second Me with other services to extend its functionality.',
    icon: '🔗',
    status: 'demo'
  }
];

export default function ApplicationsPage() {
  const handleAppClick = (app: ApplicationCard) => {
    if (app.status === 'coming-soon') {
      alert('This feature is coming soon!');
    } else if (app.status === 'demo') {
      alert('This is a demo feature. Full functionality coming soon!');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h1 className="text-3xl font-bold text-secondme-navy mb-2">
          Second Me Applications
        </h1>
        <p className="text-secondme-navy/70 max-w-3xl">
          Beyond basic chat, you can create specialized roles, personalized tasks, collaborate in
          multi-AI spaces, or explore the future with Second X.
        </p>
      </div>

      {/* Applications Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {applications.map((app, index) => (
          <div
            key={index}
            className={`bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6 transition-all cursor-pointer hover:shadow-secondme hover:-translate-y-0.5 ${
              app.status === 'coming-soon' ? 'opacity-75' : ''
            }`}
            onClick={() => handleAppClick(app)}
          >
            {/* Icon and Status */}
            <div className="flex items-center justify-between mb-4">
              <div className="text-4xl">{app.icon}</div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                app.status === 'available' ? 'bg-secondme-accent-green/10 text-secondme-accent-green' :
                app.status === 'demo' ? 'bg-secondme-accent-yellow/10 text-secondme-accent-yellow' :
                'bg-secondme-gray-300/50 text-secondme-navy/60'
              }`}>
                {app.status === 'available' ? 'Available' :
                 app.status === 'demo' ? 'Demo' :
                 'Coming Soon'}
              </div>
            </div>

            {/* Content */}
            <h2 className="text-lg font-semibold text-secondme-navy mb-2">{app.title}</h2>
            {app.subtitle && <p className="text-sm text-secondme-navy/70 mb-2">{app.subtitle}</p>}
            <p className="text-sm text-secondme-navy/70 leading-relaxed">{app.description}</p>

            {/* Action Button */}
            <div className="mt-4">
              <button className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                app.status === 'available' ? 'bg-secondme-blue text-white hover:bg-secondme-blue-hover' :
                app.status === 'demo' ? 'bg-secondme-accent-yellow/10 text-secondme-accent-yellow hover:bg-secondme-accent-yellow/20' :
                'bg-secondme-gray-300/50 text-secondme-navy/60 cursor-not-allowed'
              }`}>
                {app.status === 'available' ? 'Launch App' :
                 app.status === 'demo' ? 'Try Demo' :
                 'Coming Soon'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Development Status */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Development Roadmap</h2>
        <div className="space-y-3">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-secondme-accent-green rounded-full mr-3"></div>
            <span className="text-sm text-secondme-navy">Core AI Training System - ✅ Completed</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-secondme-accent-yellow rounded-full mr-3"></div>
            <span className="text-sm text-secondme-navy">Basic Applications Framework - 🚧 In Progress</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-secondme-gray-300 rounded-full mr-3"></div>
            <span className="text-sm text-secondme-navy">Advanced Integrations - 📋 Planned</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-secondme-gray-300 rounded-full mr-3"></div>
            <span className="text-sm text-secondme-navy">Network Collaboration - 📋 Planned</span>
          </div>
        </div>
      </div>
    </div>
  );
}
