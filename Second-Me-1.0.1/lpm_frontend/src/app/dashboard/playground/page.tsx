'use client';

import React, { useState } from 'react';

export default function PlaygroundPage() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Hello! I\'m your personalized AI trained with your memories and knowledge. How can I help you today?'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const newMessage = {
      id: messages.length + 1,
      type: 'user',
      content: inputMessage
    };

    setMessages([...messages, newMessage]);
    setInputMessage('');

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: 'assistant',
        content: 'Thanks for your message! This is a demo response. In a real implementation, this would be connected to your trained AI model.'
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-secondme-gray-300 p-4">
        <h1 className="text-2xl font-bold text-secondme-navy">AI Playground</h1>
        <p className="text-secondme-navy/70">Test your personalized AI model</p>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col bg-secondme-warm-bg">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-secondme-blue text-white'
                    : 'bg-white text-secondme-navy border border-secondme-gray-300'
                }`}
              >
                <p className="text-sm">{message.content}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Input Area */}
        <div className="bg-white border-t border-secondme-gray-300 p-4">
          <div className="flex space-x-4">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type your message..."
              className="flex-1 input-field"
            />
            <button
              onClick={handleSendMessage}
              className="px-6 py-2 bg-secondme-blue text-white rounded-xl hover:bg-secondme-blue-hover transition-colors"
            >
              Send
            </button>
          </div>
        </div>
      </div>

      {/* Model Info */}
      <div className="bg-white border-t border-secondme-gray-300 p-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-secondme-navy/70">Model:</span>
            <span className="text-secondme-navy font-medium">test_production</span>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-secondme-accent-green rounded-full mr-2"></div>
              <span className="text-secondme-accent-green">Ready</span>
            </div>
          </div>
          <div className="text-secondme-navy/70">
            Trained with 3 memory files, 9 samples
          </div>
        </div>
      </div>
    </div>
  );
}
