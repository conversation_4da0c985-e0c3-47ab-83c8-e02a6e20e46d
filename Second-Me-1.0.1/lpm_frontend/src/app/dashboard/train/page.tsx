'use client';

import React, { useState } from 'react';

export default function TrainPage() {
  const [trainingStatus, setTrainingStatus] = useState('completed');
  const [modelName] = useState('test_production');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h1 className="text-3xl font-bold text-secondme-navy mb-2">
          AI Training Center
        </h1>
        <p className="text-secondme-navy/70">
          Train your personalized AI model with your memories and knowledge.
        </p>
      </div>

      {/* Training Status */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Current Training Status</h2>

        <div className="flex items-center space-x-4 mb-4">
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-2 ${
              trainingStatus === 'completed' ? 'bg-secondme-accent-green' :
              trainingStatus === 'training' ? 'bg-secondme-accent-yellow' :
              'bg-secondme-gray-300'
            }`}></div>
            <span className="text-sm font-medium text-secondme-navy">
              {trainingStatus === 'completed' ? 'Training Completed' :
               trainingStatus === 'training' ? 'Training in Progress' :
               'Ready to Train'}
            </span>
          </div>
          <div className="text-sm text-secondme-navy/60">
            Model: {modelName}
          </div>
        </div>

        {trainingStatus === 'completed' && (
          <div className="bg-secondme-accent-green/10 border border-secondme-accent-green/20 rounded-lg p-4">
            <h3 className="text-sm font-medium text-secondme-accent-green mb-2">
              ✅ Training Successfully Completed
            </h3>
            <p className="text-sm text-secondme-navy/70">
              Your AI model has been trained with 3 memory files and 9 training samples.
              The model is now ready for use in the playground.
            </p>
          </div>
        )}
      </div>

      {/* Training Data */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Training Data</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 bg-secondme-warm-bg rounded-lg">
            <div className="text-2xl font-bold text-secondme-blue">3</div>
            <div className="text-sm text-secondme-navy/70">Memory Files</div>
          </div>
          <div className="text-center p-4 bg-secondme-warm-bg rounded-lg">
            <div className="text-2xl font-bold text-secondme-blue">9</div>
            <div className="text-sm text-secondme-navy/70">Training Samples</div>
          </div>
          <div className="text-center p-4 bg-secondme-warm-bg rounded-lg">
            <div className="text-2xl font-bold text-secondme-blue">6</div>
            <div className="text-sm text-secondme-navy/70">Topic Categories</div>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-lg font-medium text-secondme-navy">Processed Files:</h3>
          <div className="space-y-2">
            <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
              <span className="text-secondme-accent-green mr-3">📄</span>
              <div>
                <div className="text-sm font-medium text-secondme-navy">personal_memory_1.txt</div>
                <div className="text-xs text-secondme-navy/60">Personal background and values</div>
              </div>
            </div>
            <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
              <span className="text-secondme-accent-green mr-3">📄</span>
              <div>
                <div className="text-sm font-medium text-secondme-navy">personal_memory_2.txt</div>
                <div className="text-xs text-secondme-navy/60">Daily habits and thinking patterns</div>
              </div>
            </div>
            <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
              <span className="text-secondme-accent-green mr-3">📄</span>
              <div>
                <div className="text-sm font-medium text-secondme-navy">technical_knowledge.txt</div>
                <div className="text-xs text-secondme-navy/60">Technical skills and experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Training Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Training Configuration</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-secondme-navy mb-2">Model Parameters</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Learning Rate:</span>
                <span className="text-secondme-navy">0.0001</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Epochs:</span>
                <span className="text-secondme-navy">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Batch Size:</span>
                <span className="text-secondme-navy">4</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Max Length:</span>
                <span className="text-secondme-navy">512</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-secondme-navy mb-2">Training Settings</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Mode:</span>
                <span className="text-secondme-navy">Basic</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Chain of Thought:</span>
                <span className="text-secondme-accent-green">Enabled</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">CUDA:</span>
                <span className="text-secondme-navy/70">Disabled</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondme-navy/70">Threads:</span>
                <span className="text-secondme-navy">2</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Actions</h2>

        <div className="flex flex-wrap gap-4">
          <button className="btn-primary">
            Start New Training
          </button>
          <button className="px-6 py-3 text-base font-medium text-secondme-blue bg-white border-2 border-secondme-blue rounded-xl hover:bg-secondme-blue hover:text-white transition-all duration-200">
            View Training Logs
          </button>
          <button className="px-6 py-3 text-base font-medium text-secondme-navy bg-secondme-warm-bg border border-secondme-gray-300 rounded-xl hover:bg-gray-100 transition-all duration-200">
            Export Model
          </button>
        </div>
      </div>
    </div>
  );
}
