'use client';

import React from 'react';
import Link from 'next/link';

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h1 className="text-3xl font-bold text-secondme-navy mb-2">
          Welcome to Second Me Dashboard
        </h1>
        <p className="text-secondme-navy/70">
          Manage your AI training, test your models, and deploy your personalized AI.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🧠</span>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-secondme-navy">Training Status</h3>
              <p className="text-secondme-accent-green font-medium">Model: test_production</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center">
              <span className="text-2xl">📊</span>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-secondme-navy">Training Data</h3>
              <p className="text-secondme-navy/70">3 memory files processed</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-secondme-blue/10 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🚀</span>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-secondme-navy">Model Status</h3>
              <p className="text-secondme-accent-green font-medium">Ready for use</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/dashboard/train"
            className="flex flex-col items-center p-4 rounded-lg border border-secondme-gray-300 hover:border-secondme-blue hover:bg-secondme-blue/5 transition-all"
          >
            <span className="text-3xl mb-2">🧠</span>
            <span className="text-sm font-medium text-secondme-navy">Start Training</span>
          </Link>

          <Link
            href="/dashboard/playground"
            className="flex flex-col items-center p-4 rounded-lg border border-secondme-gray-300 hover:border-secondme-blue hover:bg-secondme-blue/5 transition-all"
          >
            <span className="text-3xl mb-2">🎮</span>
            <span className="text-sm font-medium text-secondme-navy">Test Model</span>
          </Link>

          <Link
            href="/dashboard/applications"
            className="flex flex-col items-center p-4 rounded-lg border border-secondme-gray-300 hover:border-secondme-blue hover:bg-secondme-blue/5 transition-all"
          >
            <span className="text-3xl mb-2">🚀</span>
            <span className="text-sm font-medium text-secondme-navy">Deploy Model</span>
          </Link>

          <div className="flex flex-col items-center p-4 rounded-lg border border-secondme-gray-300 bg-gray-50">
            <span className="text-3xl mb-2">📈</span>
            <span className="text-sm font-medium text-secondme-navy/50">Analytics</span>
            <span className="text-xs text-secondme-navy/40">Coming Soon</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-secondme-gray-300 p-6">
        <h2 className="text-xl font-semibold text-secondme-navy mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
            <div className="w-2 h-2 bg-secondme-accent-green rounded-full mr-3"></div>
            <div>
              <p className="text-sm font-medium text-secondme-navy">Model training completed</p>
              <p className="text-xs text-secondme-navy/60">test_production model is ready</p>
            </div>
          </div>

          <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
            <div className="w-2 h-2 bg-secondme-blue rounded-full mr-3"></div>
            <div>
              <p className="text-sm font-medium text-secondme-navy">Training data processed</p>
              <p className="text-xs text-secondme-navy/60">3 memory files, 9 training samples</p>
            </div>
          </div>

          <div className="flex items-center p-3 bg-secondme-warm-bg rounded-lg">
            <div className="w-2 h-2 bg-secondme-accent-yellow rounded-full mr-3"></div>
            <div>
              <p className="text-sm font-medium text-secondme-navy">System initialized</p>
              <p className="text-xs text-secondme-navy/60">Frontend and backend services started</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
