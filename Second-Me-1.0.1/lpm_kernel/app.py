from flask import Flask, request
import os
import atexit
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from lpm_kernel.common.repository.database_session import DatabaseSession, Base
    from lpm_kernel.common.logging import logger
    from lpm_kernel.api import init_routes
except ImportError as e:
    # Fallback for basic functionality
    print(f"Import warning: {e}")
    logger = None
    DatabaseSession = None
    Base = None
    init_routes = None


def create_app():
    app = Flask(__name__)

    # Initialize database connection
    try:
        if DatabaseSession:
            DatabaseSession.initialize()
            if logger:
                logger.info("Database connection initialized successfully")

    except Exception as e:
        if logger:
            logger.error(f"Failed to initialize database connection: {str(e)}")
        print(f"Database initialization failed: {e}")

    # Add CORS support

    @app.after_request
    def after_request(response):
        # Allow all origins in development environment
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add(
            "Access-Control-Allow-Headers", "Content-Type,Authorization"
        )
        response.headers.add(
            "Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS"
        )
        return response

    # Basic file serving (simplified)
    @app.route("/raw_content/", defaults={"path": ""})
    @app.route("/raw_content/<path:path>")
    def serve_content(path=""):
        return {"message": "File server not implemented"}

    # Register all routes
    if init_routes:
        init_routes(app)
    else:
        # Basic fallback routes
        @app.route('/api/health', methods=['GET'])
        def health_check():
            return {"status": "ok", "message": "Second Me API is running"}

        @app.route('/api/identity/define', methods=['POST'])
        def define_identity():
            try:
                data = request.get_json()
                return {
                    "success": True,
                    "message": "Identity defined successfully",
                    "data": data or {}
                }
            except Exception as e:
                return {"error": str(e)}, 500

        # Training process endpoints
        @app.route('/api/trainprocess/start', methods=['POST'])
        def start_training():
            try:
                data = request.get_json() or {}
                model_name = data.get('model_name', 'default_model')
                return {
                    "success": True,
                    "message": "Training started successfully",
                    "model_name": model_name,
                    "status": "started"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/trainprocess/status/<model_name>', methods=['GET'])
        def get_training_status(model_name):
            try:
                return {
                    "model_name": model_name,
                    "status": "ready",
                    "progress": 0,
                    "stage": "initialized",
                    "message": "Ready to start training"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/trainprocess/progress/<model_name>', methods=['GET'])
        def get_training_progress(model_name):
            try:
                return {
                    "model_name": model_name,
                    "status": "ready",
                    "progress": 0,
                    "current_step": "initialization",
                    "total_steps": 8,
                    "message": "Ready to start training"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/documents/list', methods=['GET'])
        def list_documents():
            try:
                return {
                    "documents": [],
                    "count": 0,
                    "message": "No documents uploaded yet"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/documents/upload', methods=['POST'])
        def upload_document():
            try:
                return {
                    "success": True,
                    "message": "Document uploaded successfully",
                    "document_id": "doc_001"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/user-llm-configs', methods=['GET'])
        def get_llm_configs():
            try:
                return {
                    "configs": [
                        {
                            "id": 1,
                            "name": "Default Config",
                            "model": "gpt-3.5-turbo",
                            "endpoint": "https://api.openai.com/v1",
                            "status": "active"
                        }
                    ]
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/trainprocess/training_params', methods=['GET'])
        def get_training_params():
            try:
                return {
                    "learning_rate": 0.0001,
                    "epochs": 3,
                    "batch_size": 4,
                    "max_length": 512,
                    "is_cot": True,
                    "concurrency_threads": 2,
                    "data_synthesis_mode": "basic"
                }
            except Exception as e:
                return {"error": str(e)}, 500

        @app.route('/api/kernel2/cuda/available', methods=['GET'])
        def cuda_available():
            try:
                return {
                    "available": False,
                    "message": "CUDA not available on this system"
                }
            except Exception as e:
                return {"error": str(e)}, 500

    # Clean up database connection only when the application shuts down
    @app.teardown_appcontext
    def cleanup_db(exception):
        pass

    return app


app = create_app()


@atexit.register
def cleanup():
    if DatabaseSession:
        DatabaseSession.close()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000, debug=False)
