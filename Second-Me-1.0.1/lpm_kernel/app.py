from flask import Flask, request
import os
import atexit
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from lpm_kernel.common.repository.database_session import DatabaseSession, Base
    from lpm_kernel.common.logging import logger
    from lpm_kernel.api import init_routes
except ImportError as e:
    # Fallback for basic functionality
    print(f"Import warning: {e}")
    logger = None
    DatabaseSession = None
    Base = None
    init_routes = None


def create_app():
    app = Flask(__name__)

    # Initialize database connection
    try:
        if DatabaseSession:
            DatabaseSession.initialize()
            if logger:
                logger.info("Database connection initialized successfully")

    except Exception as e:
        if logger:
            logger.error(f"Failed to initialize database connection: {str(e)}")
        print(f"Database initialization failed: {e}")

    # Add CORS support

    @app.after_request
    def after_request(response):
        # Allow all origins in development environment
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add(
            "Access-Control-Allow-Headers", "Content-Type,Authorization"
        )
        response.headers.add(
            "Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS"
        )
        return response

    # Basic file serving (simplified)
    @app.route("/raw_content/", defaults={"path": ""})
    @app.route("/raw_content/<path:path>")
    def serve_content(path=""):
        return {"message": "File server not implemented"}

    # Register all routes
    if init_routes:
        init_routes(app)
    else:
        # Basic fallback routes
        @app.route('/api/health', methods=['GET'])
        def health_check():
            return {"status": "ok", "message": "Second Me API is running"}

        @app.route('/api/identity/define', methods=['POST'])
        def define_identity():
            try:
                data = request.get_json()
                return {
                    "success": True,
                    "message": "Identity defined successfully",
                    "data": data or {}
                }
            except Exception as e:
                return {"error": str(e)}, 500

    # Clean up database connection only when the application shuts down
    @app.teardown_appcontext
    def cleanup_db(exception):
        pass

    return app


app = create_app()


@atexit.register
def cleanup():
    if DatabaseSession:
        DatabaseSession.close()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000, debug=False)
