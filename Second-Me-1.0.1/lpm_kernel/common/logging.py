import logging
import os

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.getcwd(), "logs")
os.makedirs(logs_dir, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(logs_dir, "backend.log")),
        logging.StreamHandler()
    ]
)

# Create logger instance
logger = logging.getLogger(__name__)

def get_logger(name=None):
    """Get a logger instance"""
    return logging.getLogger(name or __name__)