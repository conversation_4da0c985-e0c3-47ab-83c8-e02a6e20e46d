"""
L2 utilities module for model management and training utilities.
This is a placeholder implementation that needs to be completed.
"""

import os
import shutil
import subprocess
from typing import Optional
from lpm_kernel.configs.logging import get_train_process_logger

logger = get_train_process_logger()


def save_hf_model(model_name: str, cache_dir: Optional[str] = None) -> Optional[str]:
    """Download and save a Hugging Face model.
    
    Args:
        model_name: Name of the model to download
        cache_dir: Optional cache directory
        
    Returns:
        str: Path to the downloaded model, or None if failed
    """
    try:
        logger.info(f"Starting model download: {model_name}")
        
        # Set default cache directory
        if cache_dir is None:
            cache_dir = os.path.join(os.getcwd(), "resources", "L2", "base_models")
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Model path
        model_path = os.path.join(cache_dir, model_name)
        
        # Check if model already exists
        if os.path.exists(model_path) and os.path.isdir(model_path):
            config_file = os.path.join(model_path, "config.json")
            if os.path.exists(config_file):
                logger.info(f"Model {model_name} already exists at {model_path}")
                return model_path
        
        # Create model directory
        os.makedirs(model_path, exist_ok=True)
        
        try:
            # Try to use huggingface_hub to download the model
            from huggingface_hub import snapshot_download
            
            logger.info(f"Downloading model {model_name} to {model_path}")
            downloaded_path = snapshot_download(
                repo_id=model_name,
                cache_dir=model_path,
                local_dir=model_path,
                local_dir_use_symlinks=False
            )
            
            # Verify download
            config_file = os.path.join(model_path, "config.json")
            if os.path.exists(config_file):
                logger.info(f"Model {model_name} downloaded successfully to {model_path}")
                return model_path
            else:
                logger.error(f"Model download incomplete - config.json not found")
                return None
                
        except ImportError:
            logger.warning("huggingface_hub not available, trying alternative download method")
            
            # Alternative: use git clone (requires git-lfs)
            try:
                repo_url = f"https://huggingface.co/{model_name}"
                logger.info(f"Cloning model repository: {repo_url}")
                
                result = subprocess.run([
                    "git", "clone", repo_url, model_path
                ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
                
                if result.returncode == 0:
                    config_file = os.path.join(model_path, "config.json")
                    if os.path.exists(config_file):
                        logger.info(f"Model {model_name} cloned successfully to {model_path}")
                        return model_path
                    else:
                        logger.error(f"Model clone incomplete - config.json not found")
                        return None
                else:
                    logger.error(f"Git clone failed: {result.stderr}")
                    return None
                    
            except subprocess.TimeoutExpired:
                logger.error("Model download timed out")
                return None
            except Exception as e:
                logger.error(f"Alternative download method failed: {str(e)}")
                return None
        
        except Exception as e:
            logger.error(f"Model download failed: {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"Error in save_hf_model: {str(e)}")
        return None


def cleanup_model_cache(model_name: str, cache_dir: Optional[str] = None) -> bool:
    """Clean up model cache directory.
    
    Args:
        model_name: Name of the model to clean up
        cache_dir: Optional cache directory
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if cache_dir is None:
            cache_dir = os.path.join(os.getcwd(), "resources", "L2", "base_models")
        
        model_path = os.path.join(cache_dir, model_name)
        
        if os.path.exists(model_path):
            shutil.rmtree(model_path)
            logger.info(f"Model cache cleaned up: {model_path}")
            return True
        else:
            logger.info(f"Model cache does not exist: {model_path}")
            return True
            
    except Exception as e:
        logger.error(f"Error cleaning up model cache: {str(e)}")
        return False


def verify_model_integrity(model_path: str) -> bool:
    """Verify model integrity by checking required files.
    
    Args:
        model_path: Path to the model directory
        
    Returns:
        bool: True if model is valid, False otherwise
    """
    try:
        if not os.path.exists(model_path) or not os.path.isdir(model_path):
            return False
        
        # Check for required files
        required_files = ["config.json"]
        optional_files = ["pytorch_model.bin", "model.safetensors", "tokenizer.json"]
        
        # Check required files
        for file_name in required_files:
            file_path = os.path.join(model_path, file_name)
            if not os.path.exists(file_path):
                logger.error(f"Required file missing: {file_path}")
                return False
        
        # Check for at least one model file
        model_files_found = False
        for file_name in optional_files:
            file_path = os.path.join(model_path, file_name)
            if os.path.exists(file_path):
                model_files_found = True
                break
        
        if not model_files_found:
            logger.error(f"No model files found in {model_path}")
            return False
        
        logger.info(f"Model integrity verified: {model_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error verifying model integrity: {str(e)}")
        return False


def get_model_info(model_path: str) -> Optional[dict]:
    """Get model information from config file.
    
    Args:
        model_path: Path to the model directory
        
    Returns:
        dict: Model information, or None if failed
    """
    try:
        import json
        
        config_file = os.path.join(model_path, "config.json")
        if not os.path.exists(config_file):
            logger.error(f"Config file not found: {config_file}")
            return None
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        return config
        
    except Exception as e:
        logger.error(f"Error reading model info: {str(e)}")
        return None
