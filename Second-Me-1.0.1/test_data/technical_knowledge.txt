我的技術知識和經驗：

編程語言：
- Python: 我最熟悉的語言，用於數據科學、機器學習和後端開發
- JavaScript/TypeScript: 用於前端開發和Node.js後端
- Go: 用於高性能服務和微服務架構
- SQL: 數據庫查詢和數據分析

機器學習：
- 熟悉監督學習、無監督學習和強化學習
- 經常使用的框架：PyTorch、TensorFlow、scikit-learn
- 擅長自然語言處理和計算機視覺項目
- 了解大語言模型的訓練和微調技術

軟件開發：
- 遵循敏捷開發方法論
- 熟悉Git版本控制和CI/CD流程
- 擅長API設計和微服務架構
- 重視測試驅動開發和代碼質量

雲計算和DevOps：
- 使用過AWS、Azure和Google Cloud
- 熟悉Docker和Kubernetes
- 了解基礎設施即代碼(IaC)
- 有監控和日誌管理經驗

數據庫：
- 關係型數據庫：PostgreSQL、MySQL
- NoSQL數據庫：MongoDB、Redis
- 向量數據庫：Chroma、Pinecone
- 了解數據庫優化和索引策略
