#!/usr/bin/env python3
"""
Simple proxy server to forward requests from port 5000 to port 8000
This helps when the frontend is configured to call port 5000 but our backend is on port 8000
"""

from flask import Flask, request, jsonify
import requests
import json

app = Flask(__name__)

BACKEND_URL = "http://localhost:8000"

@app.route('/', defaults={'path': ''}, methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'])
@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'])
def proxy(path):
    """Proxy all requests to the backend server"""
    try:
        # Build the target URL
        target_url = f"{BACKEND_URL}/{path}"
        
        # Get request data
        data = None
        if request.method in ['POST', 'PUT', 'PATCH']:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.get_data()
        
        # Get headers (exclude host header to avoid conflicts)
        headers = {k: v for k, v in request.headers if k.lower() != 'host'}
        
        # Make the request to backend
        response = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            json=data if request.is_json else None,
            data=data if not request.is_json else None,
            params=request.args,
            timeout=30
        )
        
        # Return the response
        return response.content, response.status_code, response.headers.items()
        
    except requests.exceptions.RequestException as e:
        print(f"Proxy error: {e}")
        return jsonify({
            "error": "Backend service unavailable",
            "message": str(e),
            "proxy_info": f"Trying to reach {BACKEND_URL}/{path}"
        }), 503
    except Exception as e:
        print(f"Unexpected error: {e}")
        return jsonify({
            "error": "Proxy server error",
            "message": str(e)
        }), 500

if __name__ == "__main__":
    print("🔄 Starting proxy server on port 5001")
    print(f"📡 Forwarding requests to {BACKEND_URL}")
    app.run(host="0.0.0.0", port=5001, debug=False)
