#!/usr/bin/env python3
"""
Second Me Training Data Preparation Script
This script prepares personal memory data for training the AI model.
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataPreparer:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.test_data_dir = self.base_dir / "test_data"
        self.resources_dir = self.base_dir / "resources"
        self.data_dir = self.base_dir / "data"
        
    def prepare_directories(self):
        """Create necessary directories for training data"""
        directories = [
            self.data_dir / "training",
            self.data_dir / "processed",
            self.resources_dir / "L2" / "data_pipeline" / "raw_data",
            self.resources_dir / "raw_content"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def process_memory_files(self):
        """Process personal memory files from test_data directory"""
        if not self.test_data_dir.exists():
            logger.warning(f"Test data directory not found: {self.test_data_dir}")
            return []
        
        processed_memories = []
        memory_files = list(self.test_data_dir.glob("*.txt"))
        
        logger.info(f"Found {len(memory_files)} memory files to process")
        
        for memory_file in memory_files:
            try:
                with open(memory_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if content:
                    memory_data = {
                        "id": f"memory_{len(processed_memories) + 1}",
                        "filename": memory_file.name,
                        "content": content,
                        "type": "personal_memory",
                        "processed_at": datetime.now().isoformat(),
                        "word_count": len(content.split()),
                        "char_count": len(content)
                    }
                    processed_memories.append(memory_data)
                    logger.info(f"Processed memory file: {memory_file.name} ({memory_data['word_count']} words)")
                
            except Exception as e:
                logger.error(f"Error processing file {memory_file}: {str(e)}")
        
        return processed_memories
    
    def create_training_dataset(self, memories):
        """Create training dataset from processed memories"""
        if not memories:
            logger.warning("No memories to process for training dataset")
            return
        
        # Create training examples
        training_examples = []
        
        for memory in memories:
            # Create Q&A pairs from memory content
            content = memory["content"]
            
            # Simple example: create questions about the memory
            examples = [
                {
                    "instruction": "請描述你的技術背景和興趣",
                    "input": "",
                    "output": content,
                    "memory_id": memory["id"]
                },
                {
                    "instruction": "你是什麼樣的人？",
                    "input": "",
                    "output": content,
                    "memory_id": memory["id"]
                },
                {
                    "instruction": "告訴我關於你自己的信息",
                    "input": "",
                    "output": content,
                    "memory_id": memory["id"]
                }
            ]
            
            training_examples.extend(examples)
        
        # Save training dataset
        training_file = self.data_dir / "training" / "personal_memories_dataset.json"
        with open(training_file, 'w', encoding='utf-8') as f:
            json.dump(training_examples, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created training dataset with {len(training_examples)} examples: {training_file}")
        return training_file
    
    def create_topics_data(self, memories):
        """Create topics data from memories"""
        topics = []
        
        for memory in memories:
            # Extract potential topics from memory content
            content = memory["content"].lower()
            
            # Simple topic extraction based on keywords
            if "技術" in content or "programming" in content or "開發" in content:
                topics.append({
                    "topic": "技術與開發",
                    "keywords": ["技術", "開發", "編程", "軟件"],
                    "memory_id": memory["id"]
                })
            
            if "工作" in content or "職業" in content:
                topics.append({
                    "topic": "工作與職業",
                    "keywords": ["工作", "職業", "經驗", "公司"],
                    "memory_id": memory["id"]
                })
            
            if "興趣" in content or "愛好" in content:
                topics.append({
                    "topic": "興趣愛好",
                    "keywords": ["興趣", "愛好", "喜歡", "學習"],
                    "memory_id": memory["id"]
                })
        
        # Save topics data
        topics_file = self.resources_dir / "L2" / "data_pipeline" / "raw_data" / "topics.json"
        with open(topics_file, 'w', encoding='utf-8') as f:
            json.dump(topics, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created topics data with {len(topics)} topics: {topics_file}")
        return topics_file
    
    def create_summary_report(self, memories, training_file, topics_file):
        """Create a summary report of the data preparation process"""
        report = {
            "preparation_timestamp": datetime.now().isoformat(),
            "total_memories": len(memories),
            "total_word_count": sum(m["word_count"] for m in memories),
            "total_char_count": sum(m["char_count"] for m in memories),
            "files_processed": [m["filename"] for m in memories],
            "training_dataset": str(training_file) if training_file else None,
            "topics_file": str(topics_file) if topics_file else None,
            "status": "completed"
        }
        
        report_file = self.data_dir / "training_data_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created summary report: {report_file}")
        return report
    
    def run(self):
        """Run the complete data preparation process"""
        logger.info("Starting training data preparation...")
        
        # Step 1: Prepare directories
        self.prepare_directories()
        
        # Step 2: Process memory files
        memories = self.process_memory_files()
        
        if not memories:
            logger.error("No memories found to process. Please add memory files to test_data directory.")
            return False
        
        # Step 3: Create training dataset
        training_file = self.create_training_dataset(memories)
        
        # Step 4: Create topics data
        topics_file = self.create_topics_data(memories)
        
        # Step 5: Create summary report
        report = self.create_summary_report(memories, training_file, topics_file)
        
        logger.info("Training data preparation completed successfully!")
        logger.info(f"Summary: {report['total_memories']} memories, {report['total_word_count']} words")
        
        return True

def main():
    """Main function"""
    preparer = TrainingDataPreparer()
    success = preparer.run()
    
    if success:
        print("\n✅ Training data preparation completed successfully!")
        print("You can now proceed with model training.")
    else:
        print("\n❌ Training data preparation failed.")
        print("Please check the logs and ensure you have memory files in the test_data directory.")

if __name__ == "__main__":
    main()
