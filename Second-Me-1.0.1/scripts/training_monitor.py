#!/usr/bin/env python3
"""
Second Me Training Monitor
Real-time monitoring script for training process
"""

import os
import json
import time
import requests
from datetime import datetime
from pathlib import Path

class TrainingMonitor:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.backend_url = "http://localhost:5000"
        self.logs_dir = self.base_dir / "logs"
        self.progress_file = self.logs_dir / "train" / "progress.log"
        self.train_log_file = self.logs_dir / "train" / "train.log"
        self.backend_log_file = self.logs_dir / "backend.log"
        
        # Monitoring state
        self.last_progress_line = 0
        self.last_train_line = 0
        self.last_backend_line = 0
        self.training_active = False
        
    def check_backend_status(self):
        """Check if backend is running"""
        try:
            response = requests.get(f"{self.backend_url}/api/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_training_status(self):
        """Get current training status from API"""
        try:
            response = requests.get(f"{self.backend_url}/api/train/status", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def read_new_lines(self, file_path, last_line_count):
        """Read new lines from a log file"""
        if not file_path.exists():
            return [], last_line_count
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            new_lines = lines[last_line_count:]
            return new_lines, len(lines)
        except:
            return [], last_line_count
    
    def parse_progress_line(self, line):
        """Parse progress log line"""
        try:
            # Expected format: timestamp - Progress: X% - Stage: Y - Step: Z
            if "Progress:" in line and "Stage:" in line:
                parts = line.strip().split(" - ")
                timestamp = parts[0]
                progress_part = [p for p in parts if "Progress:" in p][0]
                stage_part = [p for p in parts if "Stage:" in p][0]
                step_part = [p for p in parts if "Step:" in p][0] if any("Step:" in p for p in parts) else ""
                
                progress = progress_part.split("Progress: ")[1].split("%")[0]
                stage = stage_part.split("Stage: ")[1]
                step = step_part.split("Step: ")[1] if step_part else ""
                
                return {
                    "timestamp": timestamp,
                    "progress": int(progress),
                    "stage": stage,
                    "step": step,
                    "raw": line.strip()
                }
        except:
            pass
        return {"raw": line.strip()}
    
    def detect_errors(self, lines):
        """Detect errors in log lines"""
        errors = []
        warnings = []
        
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in ["error", "failed", "exception", "traceback"]):
                errors.append(line.strip())
            elif any(keyword in line_lower for keyword in ["warning", "warn"]):
                warnings.append(line.strip())
        
        return errors, warnings
    
    def print_status_header(self):
        """Print monitoring header"""
        print("=" * 80)
        print("🔍 Second Me Training Monitor - Real-time Monitoring Active")
        print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
    
    def print_progress_update(self, progress_info):
        """Print progress update"""
        if "progress" in progress_info:
            print(f"📊 Progress: {progress_info['progress']}% | Stage: {progress_info['stage']}")
            if progress_info['step']:
                print(f"   Step: {progress_info['step']}")
        else:
            print(f"📝 {progress_info['raw']}")
    
    def print_errors_warnings(self, errors, warnings):
        """Print errors and warnings"""
        for error in errors:
            print(f"❌ ERROR: {error}")
        for warning in warnings:
            print(f"⚠️  WARNING: {warning}")
    
    def suggest_fixes(self, errors):
        """Suggest fixes for common errors"""
        suggestions = []
        
        for error in errors:
            error_lower = error.lower()
            
            if "connection" in error_lower or "timeout" in error_lower:
                suggestions.append("🔧 Network issue detected. Check if all services are running.")
            elif "memory" in error_lower or "out of memory" in error_lower:
                suggestions.append("🔧 Memory issue. Consider reducing batch size or clearing cache.")
            elif "permission" in error_lower or "access denied" in error_lower:
                suggestions.append("🔧 Permission issue. Check file/directory permissions.")
            elif "module" in error_lower or "import" in error_lower:
                suggestions.append("🔧 Import error. Check if all dependencies are installed.")
            elif "cuda" in error_lower or "gpu" in error_lower:
                suggestions.append("🔧 GPU issue. Consider switching to CPU mode.")
            elif "disk" in error_lower or "space" in error_lower:
                suggestions.append("🔧 Disk space issue. Free up storage space.")
        
        for suggestion in set(suggestions):
            print(suggestion)
    
    def monitor_training(self):
        """Main monitoring loop"""
        self.print_status_header()
        
        # Check backend status
        if not self.check_backend_status():
            print("❌ Backend service not responding. Please start the backend first.")
            return
        
        print("✅ Backend service is running")
        print("🔄 Monitoring training process... (Press Ctrl+C to stop)")
        print("-" * 80)
        
        try:
            while True:
                # Monitor progress log
                new_progress_lines, self.last_progress_line = self.read_new_lines(
                    self.progress_file, self.last_progress_line
                )
                
                for line in new_progress_lines:
                    progress_info = self.parse_progress_line(line)
                    self.print_progress_update(progress_info)
                    
                    if "progress" in progress_info and progress_info["progress"] > 0:
                        self.training_active = True
                
                # Monitor train log
                new_train_lines, self.last_train_line = self.read_new_lines(
                    self.train_log_file, self.last_train_line
                )
                
                # Monitor backend log
                new_backend_lines, self.last_backend_line = self.read_new_lines(
                    self.backend_log_file, self.last_backend_line
                )
                
                # Check for errors and warnings
                all_new_lines = new_train_lines + new_backend_lines
                errors, warnings = self.detect_errors(all_new_lines)
                
                if errors or warnings:
                    print("-" * 40)
                    self.print_errors_warnings(errors, warnings)
                    if errors:
                        self.suggest_fixes(errors)
                    print("-" * 40)
                
                # Get API status
                training_status = self.get_training_status()
                if training_status and self.training_active:
                    print(f"🎯 API Status: {training_status.get('status', 'Unknown')}")
                
                time.sleep(2)  # Check every 2 seconds
                
        except KeyboardInterrupt:
            print("\n" + "=" * 80)
            print("🛑 Monitoring stopped by user")
            print("=" * 80)

def main():
    monitor = TrainingMonitor()
    monitor.monitor_training()

if __name__ == "__main__":
    main()
