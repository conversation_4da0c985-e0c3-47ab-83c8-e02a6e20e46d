#!/usr/bin/env python3
"""
Test script to verify all API endpoints are working
"""

import requests
import json
import time

def test_api_endpoint(url, method='GET', data=None):
    """Test a single API endpoint"""
    try:
        if method == 'GET':
            response = requests.get(url, timeout=5)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=5)
        
        print(f"✅ {method} {url} - Status: {response.status_code}")
        if response.status_code >= 400:
            print(f"   Error: {response.text}")
        return response.status_code < 400
    except Exception as e:
        print(f"❌ {method} {url} - Error: {str(e)}")
        return False

def main():
    """Test all API endpoints"""
    print("🔍 Testing Second Me API endpoints...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        ("GET", "/api/health"),
        ("GET", "/api/system/status"),
        ("GET", "/api/trainprocess/steps"),
        ("GET", "/api/trainprocess/status/test_model"),
        ("GET", "/api/trainprocess/progress/test_model"),
        ("GET", "/api/documents/list"),
        ("GET", "/api/loads/current"),
        ("GET", "/api/upload"),
        ("GET", "/api/upload/count"),
        ("GET", "/api/user-llm-configs"),
        ("GET", "/api/trainprocess/training_params"),
        ("GET", "/api/kernel2/cuda/available"),
        ("GET", "/api/config"),
        ("POST", "/api/identity/define", {"name": "Test User"}),
        ("POST", "/api/trainprocess/start", {"model_name": "test_model"}),
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for method, path, *data in endpoints:
        url = f"{base_url}{path}"
        test_data = data[0] if data else None
        if test_api_endpoint(url, method, test_data):
            success_count += 1
        time.sleep(0.1)  # Small delay between requests
    
    print("=" * 50)
    print(f"📊 Results: {success_count}/{total_count} endpoints working")
    
    if success_count == total_count:
        print("🎉 All API endpoints are working correctly!")
    else:
        print("⚠️  Some endpoints have issues. Check the logs above.")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
