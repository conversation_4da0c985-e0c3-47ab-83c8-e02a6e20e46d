name: "🪲 Bug Report"
description: "File a bug report to help us improve"
labels: ["fix"]
body:

    - type: "markdown"
      attributes:
          value: |
              > [!IMPORTANT]
              > To save time for both you and us, try follow these guidelines before submitting a new issue:
              > 1. Check if there is an existing issue tracking your bug on our Github.
              > 2. When unsure if your issue is an actual bug, first discuss it on a [Github discussion](https://github.com/mindverse/Second-Me/discussions/new?category=q-a) or other project communication channels.
              > These steps avoid opening issues which are duplicate or not actual bugs.

    - type: "checkboxes"
      id: "environment-os"
      attributes:
          label: "Operating System"
          description: "What is your operating system?"
          options:
            - label: "macOS"
            - label: "Linux"
            - label: "Windows"
      validations:
          required: true

    - type: "checkboxes"
      id: "deployment-method"
      attributes:
          label: "Deployment Method"
          description: "How are you deploying the application?"
          options:
            - label: "Docker"
            - label: "non-Docker"
      validations:
          required: true

    - type: "checkboxes"
      id: "cuda-usage"
      attributes:
          label: "CUDA Usage"
          description: "Are you using a CUDA configuration?"
          options:
            - label: "Yes"
            - label: "No"
      validations:
          required: true

    - type: "markdown"
      attributes:
          value: |
              ---
              **If you are reporting an issue encountered during the training process, please also provide the following information:**

    - type: "textarea"
      id: "training-details"
      attributes:
          label: "Training Process Details (if applicable)"
          description: "If this bug is related to the training process, please specify: \n1. Model size (e.g., 7B, 13B, etc.) \n2. Machine base configuration (RAM size and VRAM size, e.g., 32GB RAM, 16GB VRAM)."
          placeholder: "e.g., Model: 7B, RAM: 64GB, VRAM: 24GB"
      validations:
          required: false

    - type: "markdown"
      attributes:
          value: |
              ---

    - type: "input"
      id: "version"
      attributes:
          label: "Second-Me version"
          description: "Please specify the version of Second-Me you are using (e.g., commit hash, release tag, or 'latest master'). If unsure, please indicate that."
      validations:
          required: true

    - type: "textarea"
      id: "description"
      attributes:
          label: "Describe the bug"
          description: "What is the problem? A clear and concise description of the bug."
      validations:
          required: true

    - type: "textarea"
      id: "current"
      attributes:
          label: "Current Behavior"
          description: "What actually happened?\n\n
                        Please include full errors, uncaught exceptions, stack traces, screenshots, and other relevant logs (e.g., from `log/backend.log` if applicable)."
      validations:
          required: true

    - type: "textarea"
      id: "expected"
      attributes:
          label: "Expected Behavior"
          description: "What did you expect to happen?"
      validations:
          required: true

    - type: "textarea"
      id: "reproduction"
      attributes:
          label: "Reproduction Steps"
          description: "Detail the steps needed to reproduce the issue. This can include a self-contained, concise snippet of
                        code, if applicable.\n\n
                        For more complex issues, provide a link to a repository with the smallest sample that reproduces
                        the bug.\n
                        If the issue can be replicated without code, please provide a clear, step-by-step description of
                        the actions or conditions necessary to reproduce it. Any screenshots are also appreciated.\n
                        Avoid including business logic or unrelated details, as this makes diagnosis more difficult.\n\n
                        Whether it's a sequence of actions, code samples, or specific conditions, ensure that the steps
                        are clear enough to be easily followed and replicated."
      validations:
          required: true

    - type: "textarea"
      id: "workaround"
      attributes:
          label: "Possible Workaround"
          description: "If you find any workaround for this problem - please, provide it here."
      validations:
          required: false

    - type: "textarea"
      id: "context"
      attributes:
          label: "Additional Information"
          description: "Anything else that might be relevant for troubleshooting this bug.\n
                        Providing context helps us come up with a solution that is most useful in the real-world use case."
      validations:
          required: false

    - type: "input"
      id: "discussion_link"
      attributes:
          label: "Link to related Github discussion or issue"
          description: "If there's an existing GitHub discussion or issue related to this bug, please link it here."
      validations:
          required: false
